2025-07-31 14:40:00,594 - INFO - [START] Début de l'entraînement nnUNet
2025-07-31 14:40:00,594 - INFO - [CONFIG] Configuration:
2025-07-31 14:40:00,594 - INFO -    - Dataset ID: 002
2025-07-31 14:40:00,594 - INFO -    - Configuration: 2d
2025-07-31 14:40:00,594 - INFO -    - Époques: 5
2025-07-31 14:40:00,594 - INFO -    - Validation croisée: True
2025-07-31 14:40:00,595 - INFO -    - GPU(s): 0
2025-07-31 14:40:00,595 - INFO -    - Trainer: nnUNetTrainer_5epochs
2025-07-31 14:40:00,595 - INFO - [DATASET] Dataset trouvé: Dataset002_test
2025-07-31 14:40:00,595 - INFO - [DATASET] images_dir = C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset002_test\imagesTr
2025-07-31 14:40:00,595 - INFO - [DATASET] labels_dir = C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset002_test\labelsTr
2025-07-31 14:40:00,595 - INFO - [START] Prétraitement et vérification du dataset: nnUNetv2_plan_and_preprocess -d 002 --verify_dataset_integrity -np 1
2025-07-31 14:40:24,311 - INFO - [SUCCESS] Succès: Prétraitement et vérification du dataset
2025-07-31 14:40:24,312 - INFO - [CV] Début de la validation croisée sur 5 folds
2025-07-31 14:40:24,312 - INFO - [FOLD] Début du fold 0
2025-07-31 14:40:24,312 - INFO - [START] Entraînement fold 0: nnUNetv2_train 002 2d 0 -p nnUNetPlans -tr nnUNetTrainer_5epochs --npz --c
2025-07-31 14:42:57,957 - INFO - [SUCCESS] Succès: Entraînement fold 0
2025-07-31 14:42:57,957 - INFO - [SUCCESS] Fold 0 terminé avec succès
2025-07-31 14:42:57,958 - INFO - [FOLD] Début du fold 1
2025-07-31 14:42:57,958 - INFO - [START] Entraînement fold 1: nnUNetv2_train 002 2d 1 -p nnUNetPlans -tr nnUNetTrainer_5epochs --npz --c
2025-07-31 14:45:29,743 - INFO - [SUCCESS] Succès: Entraînement fold 1
2025-07-31 14:45:29,744 - INFO - [SUCCESS] Fold 1 terminé avec succès
2025-07-31 14:45:29,744 - INFO - [FOLD] Début du fold 2
2025-07-31 14:45:29,744 - INFO - [START] Entraînement fold 2: nnUNetv2_train 002 2d 2 -p nnUNetPlans -tr nnUNetTrainer_5epochs --npz --c
2025-07-31 14:48:04,015 - INFO - [SUCCESS] Succès: Entraînement fold 2
2025-07-31 14:48:04,015 - INFO - [SUCCESS] Fold 2 terminé avec succès
2025-07-31 14:48:04,015 - INFO - [FOLD] Début du fold 3
2025-07-31 14:48:04,016 - INFO - [START] Entraînement fold 3: nnUNetv2_train 002 2d 3 -p nnUNetPlans -tr nnUNetTrainer_5epochs --npz --c
2025-07-31 14:50:51,733 - INFO - [SUCCESS] Succès: Entraînement fold 3
2025-07-31 14:50:51,734 - INFO - [SUCCESS] Fold 3 terminé avec succès
2025-07-31 14:50:51,734 - INFO - [FOLD] Début du fold 4
2025-07-31 14:50:51,734 - INFO - [START] Entraînement fold 4: nnUNetv2_train 002 2d 4 -p nnUNetPlans -tr nnUNetTrainer_5epochs --npz --c
2025-07-31 14:53:33,309 - INFO - [SUCCESS] Succès: Entraînement fold 4
2025-07-31 14:53:33,310 - INFO - [SUCCESS] Fold 4 terminé avec succès
2025-07-31 14:53:33,310 - INFO - [RESULTS] Résultats de la validation croisée:
2025-07-31 14:53:33,310 - INFO - [SUCCESS] Folds réussis: [0, 1, 2, 3, 4]
2025-07-31 14:53:33,310 - INFO - [SEARCH] Recherche de la meilleure configuration...
2025-07-31 14:53:33,310 - INFO - [START] Recherche de la meilleure configuration et création de l'ensemble: nnUNetv2_find_best_configuration 002 -c 2d -tr nnUNetTrainer_5epochs
2025-07-31 14:53:52,395 - INFO - [SUCCESS] Succès: Recherche de la meilleure configuration et création de l'ensemble
2025-07-31 14:53:52,395 - INFO - [SUCCESS] Configuration optimale trouvée !
2025-07-31 14:53:52,395 - INFO - [FILES] Fichiers générés :
2025-07-31 14:53:52,395 - INFO -    - inference_instructions.txt
2025-07-31 14:53:52,395 - INFO -    - inference_information.json
2025-07-31 14:53:52,395 - INFO -    - postprocessing.pkl
2025-07-31 14:53:52,395 - INFO - [INFO] Consultez inference_instructions.txt pour les commandes d'inférence
2025-07-31 14:53:52,395 - INFO - [COMPLETE] Processus complet terminé en 0.23 heures
