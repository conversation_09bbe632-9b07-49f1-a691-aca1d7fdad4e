"""
Utilitaire pour analyser les labels dans les masques de segmentation.
Analyse les valeurs uniques présentes dans chaque masque et génère un rapport.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import time
from datetime import datetime
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_label(label_path, output_file):
    """
    Analyse une image de label et écrit les résultats dans un fichier.

    Args:
        label_path: Chemin vers l'image de label
        output_file: Fichier ouvert pour écrire les résultats

    Returns:
        bool: True si l'analyse a réussi, False sinon
    """
    try:
        # Charger l'image
        label = cv2.imread(str(label_path), cv2.IMREAD_GRAYSCALE)
        if label is None:
            error_msg = f"❌ Impossible de charger l'image: {label_path.name}"
            output_file.write(f"{error_msg}\n")
            return False

        # Trouver les valeurs uniques
        unique_values = np.unique(label)
        result_msg = f"{label_path.name}: {unique_values.tolist()}"

        # Écrire dans le fichier (sans flush constant pour améliorer les performances)
        output_file.write(f"{result_msg}\n")

        return True

    except Exception as e:
        error_msg = f"❌ Erreur lors de l'analyse de {label_path.name}: {str(e)}"
        output_file.write(f"{error_msg}\n")
        return False

def analyze_masks_directory(masks_dir, output_dir=None, verbose=True):
    """
    Analyse tous les masques dans un dossier et génère un rapport.

    Args:
        masks_dir (str): Chemin vers le dossier contenant les masques
        output_dir (str, optional): Dossier de sortie pour le rapport. Si None, utilise masks_dir
        verbose (bool): Mode verbeux pour l'affichage

    Returns:
        tuple: (nombre_de_fichiers_analysés, nombre_de_succès, nombre_d'échecs, chemin_du_rapport)
    """
    masks_dir = Path(masks_dir)
    if output_dir is None:
        output_dir = masks_dir
    else:
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

    # Lister tous les fichiers .png dans le dossier
    label_files = list(masks_dir.glob("*.png"))

    if not label_files:
        logger.error("❌ Aucun fichier .png trouvé dans le dossier des masques")
        return 0, 0, 0, None

    total_files = len(label_files)
    logger.info(f"📁 {total_files} fichiers trouvés dans {masks_dir}")

    # Créer le fichier de sortie avec timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"label_analysis_results_{timestamp}.txt"
    output_path = output_dir / output_filename

    logger.info(f"💾 Sauvegarde des résultats dans: {output_path}")
    if verbose:
        logger.info(f"🔄 Mode verbeux: {'Activé' if verbose else 'Désactivé'}")
    logger.info("-" * 70)

    start_time = time.time()
    successful_analyses = 0
    failed_analyses = 0

    # Ouvrir le fichier de sortie
    with open(output_path, 'w', encoding='utf-8') as output_file:
        # Écrire l'en-tête
        output_file.write(f"Analyse des labels - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        output_file.write(f"Dossier analysé: {masks_dir}\n")
        output_file.write(f"Nombre total de fichiers: {total_files}\n")
        output_file.write("=" * 70 + "\n\n")

        # Analyser chaque fichier
        for i, label_file in enumerate(label_files, 1):
            # Analyser le fichier
            success = analyze_label(label_file, output_file)

            if success:
                successful_analyses += 1
            else:
                failed_analyses += 1

            # Afficher un résumé périodique (tous les 1000 fichiers pour réduire la sortie console)
            if verbose and i % 1000 == 0:
                elapsed = time.time() - start_time
                progress_percent = (i / total_files) * 100
                avg_time_per_file = elapsed / i if i > 0 else 0
                estimated_remaining = avg_time_per_file * (total_files - i)

                logger.info(f"📊 [{i:6d}/{total_files:6d}] ({progress_percent:5.1f}%) - {elapsed:.1f}s écoulées - ETA: {estimated_remaining:.1f}s")
                logger.info(f"   ✅ Réussis: {successful_analyses}, ❌ Échecs: {failed_analyses}")

                # Flush le fichier périodiquement pour sauvegarder les résultats
                output_file.flush()

    # Résumé final
    total_time = time.time() - start_time
    logger.info("\n" + "=" * 70)
    logger.info("📋 RÉSUMÉ FINAL")
    logger.info("=" * 70)
    logger.info(f"📁 Fichiers traités: {total_files}")
    logger.info(f"✅ Analyses réussies: {successful_analyses}")
    logger.info(f"❌ Analyses échouées: {failed_analyses}")
    logger.info(f"⏱️  Temps total: {total_time:.2f} secondes")
    if total_time > 0:
        logger.info(f"⚡ Vitesse moyenne: {total_files/total_time:.2f} fichiers/seconde")
    logger.info(f"💾 Résultats sauvegardés dans: {output_path}")
    logger.info("=" * 70)

    return total_files, successful_analyses, failed_analyses, str(output_path)

def analyze_inference_results(inference_output_dir, verbose=True):
    """
    Analyse les résultats d'inférence nnUNet et génère un rapport des labels.

    Args:
        inference_output_dir (str): Dossier de sortie de l'inférence nnUNet
        verbose (bool): Mode verbeux pour l'affichage

    Returns:
        tuple: (nombre_de_fichiers_analysés, nombre_de_succès, nombre_d'échecs, chemin_du_rapport)
    """
    logger.info(f"[LABEL_ANALYSIS] Analyse des résultats d'inférence dans: {inference_output_dir}")
    return analyze_masks_directory(inference_output_dir, verbose=verbose)

if __name__ == "__main__":
    # Exemple d'utilisation
    masks_dir = Path(r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data\masques")
    
    if masks_dir.exists():
        analyze_masks_directory(masks_dir, verbose=True)
    else:
        print(f"❌ Le dossier {masks_dir} n'existe pas")
