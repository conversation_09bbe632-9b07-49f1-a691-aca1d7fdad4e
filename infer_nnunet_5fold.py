import os
import subprocess
from utils.visualisation import reconvert_predictions
from utils.conversion import convert_images_to_grayscale
from utils.rename import rename_imagesTr, initial_rename
from utils.overlay_manager import OverlayManager
import cv2
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === CONFIGURATION ===
DATASET_ID = "011_uint8"
CONFIGURATION = "2d"
FOLD_LIST = ["0", "1", "2", "3", "4"]
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5
GPU_ID = "0"

# === DOSSIERS ===
INPUT_FOLDER =r"C:\Users\<USER>\Documents\4Corrosion\Dataset\imagesTestInference_uint8"
BASE_OUTPUT_ROOT = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference"
# INPUT_FOLDER = "/mnt/Datasets/nnUnet/inference/imagesTestInference_uint8"
# BASE_OUTPUT_ROOT = "/mnt/results/inference"
input_name = os.path.basename(INPUT_FOLDER.rstrip("\\/")).replace(" ", "_")
output_candidate = os.path.join(BASE_OUTPUT_ROOT, input_name)

OUTPUT_FOLDER = output_candidate
version = 2
while os.path.exists(OUTPUT_FOLDER):
    OUTPUT_FOLDER = f"{output_candidate}_v{version}"
    version += 1
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# === PATHS ===
RAW_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw"
PREPROCESSED_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_preprocessed"
RESULTS_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Results\nnUNet_results"

# # === CHEMINS (MODIFIEZ SELON VOTRE ENVIRONNEMENT) ===
# RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
# PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
# RESULTS_PATH = "/mnt/results/nnUnet_results"

os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID

# === CONVERSION ET RENOMMAGE DES IMAGES D'ENTRÉE ===
print("\n[INFO] Prétraitement des images d'entrée (format + couleur)...")
convert_images_to_grayscale(INPUT_FOLDER)

print("\n[INFO] Première passe de renommage...")
initial_rename(INPUT_FOLDER)

print("\n[INFO] Deuxième passe de renommage (format nnU-Net)...")
rename_imagesTr(INPUT_FOLDER)

# === CONSTRUCTION DE LA COMMANDE D'INFÉRENCE ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
folds_str = " ".join(FOLD_LIST)
cmd_predict = (
    f'nnUNetv2_predict -d Dataset{DATASET_ID} -i "{INPUT_FOLDER}" -o "{OUTPUT_FOLDER}" '
    f'-f {folds_str} -tr {trainer_class} -c {CONFIGURATION} -p {PLANS_NAME}'
)

def run(cmd):
    print(f"\n[INFO] Lancement : {cmd}\n")
    subprocess.run(cmd, shell=True, check=True)

print(f"[INFO] Dossier de sortie : {OUTPUT_FOLDER}")
run(cmd_predict)

# === POST-INFERENCE : Reconversion + Visualisation ===
RECONVERTED_MASKS = os.path.join(OUTPUT_FOLDER, "reconverted_masks")
OVERLAY_DIR = os.path.join(OUTPUT_FOLDER, "overlays")

print("\n[INFO] Post-traitement des masques prédits...")
reconvert_predictions(
    input_dir=OUTPUT_FOLDER,
    output_dir=RECONVERTED_MASKS,
    class_to_value={
        0: 0,
        1: 29,
        2: 149,
        3: 76,
        4: 125
    }
)

print("\n[INFO] Création des overlays...")
os.makedirs(OVERLAY_DIR, exist_ok=True)
overlay_manager = OverlayManager()

for img_name in os.listdir(INPUT_FOLDER):
    if img_name.endswith(('.png', '.jpg', '.jpeg')):
        try:
            img_path = os.path.join(INPUT_FOLDER, img_name)
            logger.info(f"Traitement de l'image: {img_name}")
            original_img = cv2.imread(img_path)
            if original_img is None:
                logger.error(f"Impossible de charger l'image: {img_path}")
                continue

            img_number = img_name.split('_')[0]
            mask_name = f"{img_number}.png"
            mask_path = os.path.join(RECONVERTED_MASKS, mask_name)

            if not os.path.exists(mask_path):
                logger.error(f"Masque non trouvé: {mask_path}")
                continue

            mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            if mask_img is None:
                logger.error(f"Impossible de charger le masque: {mask_path}")
                continue

            overlay = overlay_manager.create_high_contrast_overlay(original_img, mask_img, alpha=0.8)
            output_path = os.path.join(OVERLAY_DIR, f"overlay_{img_name}")
            overlay_manager.save_overlay(overlay, output_path)
            logger.info(f"Overlay sauvegardé: {output_path}")
        except Exception as e:
            logger.error(f"Erreur lors du traitement de {img_name}: {str(e)}")
            continue

# === POSTPROCESSING ===
print("\n[INFO] Application du postprocessing...")

# Chemin vers le dossier des résultats du training (crossval)
RESULT_TRAINING_DIR = os.path.join(
    RESULTS_PATH, f"Dataset{DATASET_ID}", f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}", 
    f"crossval_results_folds_{'_'.join(FOLD_LIST)}"
)

POSTPROCESSING_PKL = os.path.join(RESULT_TRAINING_DIR, "postprocessing.pkl")
PLANS_JSON = os.path.join(RESULT_TRAINING_DIR, "plans.json")
OUTPUT_FOLDER_PP = OUTPUT_FOLDER + "_PP"

cmd_postprocess = (
    f'nnUNetv2_apply_postprocessing -i "{OUTPUT_FOLDER}" -o "{OUTPUT_FOLDER_PP}" '
    f'-pp_pkl_file "{POSTPROCESSING_PKL}" -np 1 -plans_json "{PLANS_JSON}"'
)

run(cmd_postprocess)

print("\n[SUCCESS] Inférence, post-traitement et postprocessing terminés.")
