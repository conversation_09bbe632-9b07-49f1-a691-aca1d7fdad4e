import os
import shutil
import numpy as np
from PIL import Image

def backup_folder(src_folder):
    """
    Crée un backup du dossier en le copiant dans <src_folder>_backup
    """
    backup_dir = src_folder.rstrip("\\/") + "_backup"
    if not os.path.exists(backup_dir):
        shutil.copytree(src_folder, backup_dir)
        print(f"📦 Backup créé : {backup_dir}")
    else:
        print(f"ℹ️ Backup déjà existant : {backup_dir}")
    return backup_dir

def convert_images_to_grayscale(images_dir):
    """
    Convertit les images en niveaux de gris (mode 'L') seulement si nécessaire.
    Sauvegarde les originaux dans un dossier *_backup.
    """
    backup_folder(images_dir)

    for fname in os.listdir(images_dir):
        if fname.lower().endswith(".png"):
            path = os.path.join(images_dir, fname)
            with Image.open(path) as img:
                if img.mode != "L":
                    img = img.convert("L")
                    img.save(path)
                    print(f"✅ Image convertie (grayscale) : {fname}")
                else:
                    print(f"⏩ Image déjà en grayscale : {fname}")

def detect_binary_mask_format(labels_dir):
    """
    Détecte automatiquement si les masques sont en format binaire (0, 255)
    et retourne le mapping approprié pour la conversion.
    Tous les fichiers doivent avoir le même format binaire pour être détectés.
    """
    sample_files = []
    all_unique_values = set()

    # Analyser quelques fichiers pour détecter le format
    for fname in os.listdir(labels_dir):
        if fname.lower().endswith(".png"):
            sample_files.append(fname)
            if len(sample_files) >= 5:  # Analyser max 5 fichiers
                break

    # Collecter toutes les valeurs uniques de tous les fichiers
    for fname in sample_files:
        path = os.path.join(labels_dir, fname)
        mask = np.array(Image.open(path).convert("L"))
        unique_vals = set(np.unique(mask))
        all_unique_values.update(unique_vals)

        print(f"🔍 Analyse {fname}: valeurs uniques {sorted(unique_vals)}")

    # Vérifier si c'est un format binaire cohérent
    if all_unique_values == {0, 255}:
        print(f"✅ Format binaire standard détecté (0, 255) - cohérent sur tous les fichiers")
        return {0: 0, 255: 1}
    elif len(all_unique_values) == 2 and 255 in all_unique_values:
        other_val = [v for v in all_unique_values if v != 255][0]
        print(f"✅ Format binaire avec 255 détecté ({other_val}, 255) - cohérent sur tous les fichiers")
        return {other_val: 0, 255: 1}
    else:
        print(f"⚠️  Format non-binaire ou incohérent détecté: {sorted(all_unique_values)}")
        return None

def convert_labels_to_classes(labels_dir, val_to_class=None, auto_detect_binary=True):
    """
    Convertit les valeurs brutes des masques en entiers de classes (0,1,2...).
    Ne reconvertit pas si le masque contient déjà uniquement les classes attendues.

    Args:
        labels_dir: Dossier contenant les masques
        val_to_class: Mapping manuel des valeurs vers les classes (optionnel si auto_detect_binary=True)
        auto_detect_binary: Si True, détecte automatiquement les masques binaires avec valeur 255
    """
    # Détection automatique du format binaire si aucun mapping fourni
    if val_to_class is None and auto_detect_binary:
        print("🔍 Détection automatique du format des masques...")
        val_to_class = detect_binary_mask_format(labels_dir)

        if val_to_class is None:
            raise ValueError("Aucun format détecté automatiquement. Veuillez fournir val_to_class manuellement.")
        else:
            print(f"✅ Format détecté automatiquement: {val_to_class}")

    if val_to_class is None:
        raise ValueError("val_to_class requis ou activez auto_detect_binary=True")

    expected_classes = set(val_to_class.values())
    backup_folder(labels_dir)

    for fname in os.listdir(labels_dir):
        if fname.lower().endswith(".png"):
            path = os.path.join(labels_dir, fname)
            mask = np.array(Image.open(path).convert("L"))

            unique_vals = set(np.unique(mask))
            if unique_vals.issubset(expected_classes):
                print(f"⏩ Masque déjà converti : {fname}")
                continue

            class_mask = np.zeros_like(mask, dtype=np.uint8)
            for val, cls in val_to_class.items():
                class_mask[mask == val] = cls

            Image.fromarray(class_mask, mode="L").save(path)
            print(f"✅ Masque converti (classes) : {fname} - {unique_vals} → {set(np.unique(class_mask))}")

def analyze_masks_format(labels_dir, max_files=5):
    """
    Analyse le format des masques dans un dossier et affiche des statistiques détaillées.
    Utile pour diagnostiquer les problèmes de format avant la conversion.

    Args:
        labels_dir: Dossier contenant les masques à analyser
        max_files: Nombre maximum de fichiers à analyser (défaut: 5)
    """
    print(f"\n🔍 Analyse du format des masques dans: {labels_dir}")
    print("=" * 60)

    files_analyzed = 0
    all_unique_values = set()
    format_summary = {}

    for fname in sorted(os.listdir(labels_dir)):
        if fname.lower().endswith(".png") and files_analyzed < max_files:
            path = os.path.join(labels_dir, fname)
            mask = np.array(Image.open(path).convert("L"))
            unique_vals = sorted(np.unique(mask))
            all_unique_values.update(unique_vals)

            # Créer une signature du format
            format_key = tuple(unique_vals)
            if format_key not in format_summary:
                format_summary[format_key] = []
            format_summary[format_key].append(fname)

            print(f"📄 {fname}: {unique_vals} (shape: {mask.shape})")
            files_analyzed += 1

    print("\n📊 Résumé des formats détectés:")
    print("-" * 40)

    for format_vals, files in format_summary.items():
        print(f"Format {list(format_vals)}: {len(files)} fichier(s)")
        if len(files) <= 3:
            print(f"  Exemples: {', '.join(files)}")
        else:
            print(f"  Exemples: {', '.join(files[:3])}... (+{len(files)-3} autres)")

    print(f"\n🎯 Toutes les valeurs uniques trouvées: {sorted(all_unique_values)}")

    # Suggestions automatiques
    print("\n💡 Suggestions de conversion:")
    if all_unique_values == {0, 255}:
        print("✅ Format binaire standard détecté (0, 255)")
        print("   Mapping suggéré: {0: 0, 255: 1}")
    elif len(all_unique_values) == 2 and 255 in all_unique_values:
        other_val = [v for v in all_unique_values if v != 255][0]
        print(f"✅ Format binaire avec 255 détecté ({other_val}, 255)")
        print(f"   Mapping suggéré: {{{other_val}: 0, 255: 1}}")
    elif all_unique_values.issubset({0, 1, 2, 3, 4, 5}):
        print("✅ Format classes déjà converti détecté")
        print("   Aucune conversion nécessaire")
    else:
        print("⚠️  Format personnalisé détecté")
        print("   Vous devrez fournir un mapping manuel val_to_class")

        # Suggestion de mapping automatique
        sorted_vals = sorted(all_unique_values)
        suggested_mapping = {val: idx for idx, val in enumerate(sorted_vals)}
        print(f"   Mapping suggéré: {suggested_mapping}")

    print("=" * 60)
