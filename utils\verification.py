import os
from PIL import Image

def check_image(path, expected_size=(512, 512), label=False):
    """
    Vérifie l'intégrité d'une image :
    - Pas corrompue
    - Taille correcte
    - Mode compatible (grayscale ou RGB)
    """
    try:
        img = Image.open(path)
        img.verify()
        img = Image.open(path)  # recharge après .verify()
        size_ok = img.size == expected_size
        mode_ok = (img.mode == 'L') if label else (img.mode in ['L', 'RGB'])
        return size_ok and mode_ok, img.mode, img.size
    except Exception as e:
        return False, str(e), None

def verify_dataset_integrity(imagesTr_dir, labelsTr_dir, expected_size=(512, 512)):
    """
    Vérifie toutes les imagesTr et labelsTr :
    - Dimensions = expected_size
    - Formats compatibles avec nnU-Net
    """
    print("🔎 Vérification des imagesTr :")
    for fname in sorted(os.listdir(imagesTr_dir)):
        if fname.lower().endswith(".png"):
            ok, mode, size = check_image(os.path.join(imagesTr_dir, fname), expected_size, label=False)
            if not ok:
                print(f"❌ {fname} | Problème : mode={mode}, size={size}")
            else:
                print(f"✅ {fname} | mode={mode}, size={size}")

    print("\n🔎 Vérification des labelsTr :")
    for fname in sorted(os.listdir(labelsTr_dir)):
        if fname.lower().endswith(".png"):
            ok, mode, size = check_image(os.path.join(labelsTr_dir, fname), expected_size, label=True)
            if not ok:
                print(f"❌ {fname} | Problème : mode={mode}, size={size}")
            else:
                print(f"✅ {fname} | mode={mode}, size={size}")
